<template>
  <SearchForm :model="model" @search="search" @reset="reset" btn-span="24 s:12 m:4">
    <NFormItemGi span="24 s:12 m:6" label="纳税人识别号" path="etaxNsrsbh" class="pr-24px whitespace-nowrap" label-style="margin-right: 20px;">
      <NInput v-model:value="model.etaxNsrsbh" placeholder="请输入纳税人识别号" clearable />
    </NFormItemGi>
<!--    <NFormItemGi span="24 s:12 m:6" label="状态" path="status" class="pr-24px" label-style="margin-right: 12px;">-->
<!--      <NSelect -->
<!--        v-model:value="model.status" -->
<!--        :options="statusOptions" -->
<!--        placeholder="请选择状态" -->
<!--        clearable -->
<!--      />-->
<!--    </NFormItemGi>-->
    <NFormItemGi span="24 s:12 m:12" label="时间范围" path="timeRange" class="pr-24px" label-style="margin-right: 12px;">
      <DatetimeRange v-model:value="model.timeRange!" class="w-full" />
    </NFormItemGi>
  </SearchForm>
</template>

<script setup lang="ts">
import { $t } from '@/locales';
import DatetimeRange from '@/components/common/datetime-range.vue';
import SearchForm from '@/components/common/search-form.vue';

defineOptions({
  name: 'LogBusinessSearch'
});

interface StatusOption {
  label: string;
  value: string;
}

const statusOptions: StatusOption[] = [
  { label: '导出中', value: '0' },
  { label: '导出成功', value: '1' },
  { label: '导出失败', value: '2' }
];

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const model = defineModel<{
  etaxNsrsbh: string;
  status: string;
  timeRange: [string, string] | null;
}>('model', { required: true });

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<style scoped></style>
