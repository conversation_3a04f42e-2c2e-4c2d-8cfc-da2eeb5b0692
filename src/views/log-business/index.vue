<script setup lang="ts">
import { onMounted, ref } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { useMessage } from 'naive-ui';
import { fetchBusinessLogs, type BusinessLog } from '@/service/api/log';
import { useAppStore } from '@/store/modules/app';
import LogBusinessSearch from './modules/log-business-search.vue';

const message = useMessage();
const appStore = useAppStore();

const searchParams = ref({
  etaxNsrsbh: '',
  status: '',
  timeRange: null as [string, string] | null
});

const loading = ref(false);
const tableData = ref<BusinessLog[]>([]);

const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  itemCount: 0,
  onChange: (page: number) => {
    pagination.value.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize;
    pagination.value.page = 1;
  }
});

const columns: DataTableColumns<BusinessLog> = [
  {
    title: '纳税人识别号',
    key: 'etaxNsrsbh',
    width: 180
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      return row.status === '0' ? '导出中' : row.status === '1' ? '导出成功' : row.status === '2' ? '导出失败' : row.status;
    }
  },
  {
    title: '请求参数',
    key: 'jsonData',
    width: 300,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '消息体',
    key: 'message',
    width: 300,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180
  },
  {
    title: '更新时间',
    key: 'updateTime',
    width: 180
  }
];

async function loadTableData() {
  loading.value = true;
  try {
    const params = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
      etaxNsrsbh: searchParams.value.etaxNsrsbh,
      status: searchParams.value.status,
      startTime: searchParams.value.timeRange?.[0] ? new Date(searchParams.value.timeRange[0]).getTime() : undefined,
      endTime: searchParams.value.timeRange?.[1] ? new Date(searchParams.value.timeRange[1]).getTime() : undefined
    };

    console.log('请求参数:', params);
    const { data } = await fetchBusinessLogs(params);
    console.log('接口返回数据:', data);

    if (data) {
      tableData.value = data.data;
      pagination.value.itemCount = data.total;
      console.log('更新后的表格数据:', tableData.value);
      console.log('更新后的分页数据:', pagination.value);
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  pagination.value.page = 1;
  loadTableData();
}

function resetSearchParams() {
  searchParams.value = {
    etaxNsrsbh: '',
    status: '',
    timeRange: null
  };
  handleSearch();
}

function handlePageChange(page: number) {
  pagination.value.page = page;
  loadTableData();
}

function handlePageSizeChange(pageSize: number) {
  pagination.value.pageSize = pageSize;
  pagination.value.page = 1;
  loadTableData();
}

onMounted(() => {
  loadTableData();
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <LogBusinessSearch v-model:model="searchParams" @reset="resetSearchParams" @search="handleSearch" />
    <NCard
      title="业务日志"
      :bordered="false"
      size="small"
      class="sm:flex-1-hidden card-wrapper"
      header-class="view-card-header"
    >
      <template #header-extra>
        <NButton circle secondary @click="loadTableData">
          <template #icon>
            <i class="i-material-symbols:refresh" />
          </template>
        </NButton>
      </template>
      <NDataTable
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :flex-height="!appStore.isMobile"
        :scroll-x="1200"
        remote
        :pagination="pagination"
        class="sm:h-full"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </NCard>
  </div>
</template>
