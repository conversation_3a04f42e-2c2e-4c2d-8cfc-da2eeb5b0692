
import { request } from '@/service/request';

export interface LoginLogParams {
  page: number;
  pageSize: number;
  khid?: string;
  nsrsbh?: string;
  startTime?: number;
  endTime?: number;
}

export interface LoginLogResponse {
  data: LoginLog[];
  total: number;
}

export interface LoginLog {
  khid: string;
  nsrsbh: string;
  retcode: string;
  retmsg: string;
  createTime: string;
  updateTime: string;
}

export interface BusinessLogParams {
  page: number;
  pageSize: number;
  etaxNsrsbh?: string;
  status?: string;
  startTime?: number;
  endTime?: number;
}

export interface BusinessLogResponse {
  data: BusinessLog[];
  total: number;
}

export interface BusinessLog {
  id?: string | number;
  etaxNsrsbh: string;
  status: string;
  jsonData: string;
  message: string;
  createTime: string;
  updateTime: string;
}

export function fetchLoginLogs(params: LoginLogParams) {
  return request<LoginLogResponse>({
    url: '/api/log/login/list',
    method: 'get',
    params
  });
}

export function fetchBusinessLogs(params: BusinessLogParams) {
  return request<BusinessLogResponse>({
    url: '/api/log/business/list',
    method: 'get',
    params
  });
}
